// Package main 是Go Web应用程序入口点
package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"
	"web_app/dao/mysql"
	"web_app/dao/redis"
	"web_app/logger"
	"web_app/routes"
	"web_app/settings"

	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// main 应用程序入口点
func main() {
	// 从命令行参数中获取配置文件路径
	if len(os.Args) < 2 {
		return
	}
	//os.Args[1] 是命令行参数中下标为1的参数 即配置文件路径 下标为0的是程序名
	if err := settings.Init(os.Args[1]); err != nil {
		fmt.Printf("初始化配置失败, 错误:%v\n", err)
		return
	}

	// 初始化日志
	if err := logger.Init(); err != nil {
		fmt.Printf("初始化日志系统失败, 错误:%v\n", err)
		return
	}
	defer zap.L().Sync()
	zap.L().Debug("日志系统初始化成功...")

	// 初始化MySQL
	if err := mysql.Init(); err != nil {
		fmt.Printf("初始化MySQL失败, 错误:%v\n", err)
		return
	}
	defer mysql.Close()

	// 初始化Redis
	if err := redis.Init(); err != nil {
		fmt.Printf("初始化Redis失败, 错误:%v\n", err)
		return
	}
	defer redis.Close()

	// 初始化路由
	r := routes.SetupRouter()

	// 配置HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", viper.GetInt("app.port")),
		Handler: r,
	}

	// 启动HTTP服务器
	go func() {
		zap.L().Info("正在启动HTTP服务器", zap.Int("port", viper.GetInt("app.port")))
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("服务器启动失败: %s\n", err)
		}
	}()

	// 优雅关机
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	zap.L().Info("收到关闭信号，开始优雅关机...")

	// 5秒超时关机
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 关闭服务器
	if err := srv.Shutdown(ctx); err != nil {
		zap.L().Fatal("服务器强制关闭", zap.Error(err))
	}

	zap.L().Info("服务器已安全退出")
}
