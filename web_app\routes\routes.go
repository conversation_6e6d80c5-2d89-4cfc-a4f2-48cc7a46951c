// Package routes 负责HTTP路由配置和中间件管理
package routes

import (
	"net/http"
	"web_app/logger"

	"github.com/gin-gonic/gin"
)

// SetupRouter 配置和初始化路由
func SetupRouter() *gin.Engine {
	// 创建路由引擎
	r := gin.New()

	// 配置全局中间件
	r.Use(logger.GinLogger(), logger.GinRecovery(true))

	// 健康检查接口
	r.GET("/ping", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "pong",
			"status":  "healthy",
			"service": "Goweb-Frame",
		})
	})

	// 返回配置完成的路由引擎
	return r
}
