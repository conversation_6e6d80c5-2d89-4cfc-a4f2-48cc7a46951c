// Package routes 负责HTTP路由配置和中间件管理
package routes

import (
	"net/http"
	"web_app/controllers"
	"web_app/logger"
	"web_app/middlewares"

	"github.com/gin-gonic/gin"
)

// SetupRouter 配置和初始化路由
func SetupRouter() *gin.Engine {

	// 创建路由引擎
	r := gin.New()

	// 配置全局中间件
	r.Use(logger.GinLogger(), logger.GinRecovery(true))

	// 注册业务路由
	r.POST("/signup", controllers.SignUpHandler)
	r.POST("/login", controllers.LoginHandler)

	// 健康检查接口
	r.GET("/ping", middlewares.JWTAuthMiddleware(), func(c *gin.Context) {
		isLogin := true
		c.Request.Header.Set("Authorization", "Bearer "+c.GetHeader("Authorization"))
		if isLogin {
			c.JSON(http.StatusOK, gin.H{
				"message": "pong",
				"status":  "healthy",
				"service": "Goweb-Frame",
			})
		}
	})

	// 返回配置完成的路由引擎
	return r
}
