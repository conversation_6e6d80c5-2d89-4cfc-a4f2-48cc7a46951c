{"level":"DEBUG","time":"2025-08-21T00:50:03.958+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:03.967+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:03.970+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"ERROR","time":"2025-08-21T00:50:03.975+0800","caller":"redis/redis.go:73","msg":"Redis 连接失败","error":"ERR Client sent AUTH, but no password is set"}
{"level":"INFO","time":"2025-08-21T00:50:03.976+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:50:39.263+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:39.270+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:39.272+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:56:47.078+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:56:47.086+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:56:47.087+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:57:03.971+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:57:11.973+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:57:11.982+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:57:11.984+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:58.820+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:57:59.083+0800","caller":"logger/logger.go:81","msg":"/favicon.ico","status":404,"method":"GET","path":"/favicon.ico","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:16.267+0800","caller":"logger/logger.go:81","msg":"/","status":404,"method":"GET","path":"/","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:28.264+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:04:33.668+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:04:33.676+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:04:33.678+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:04:40.377+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:04:43.268+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:06:05.362+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:06:05.369+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:06:05.371+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:14:42.857+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:14:42.866+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:14:42.867+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:27:35.532+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:27:35.542+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:30:52.312+0800","caller":"web_app/main.go:37","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T01:30:52.321+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"web_app/main.go:64","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:75","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:86","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:30:59.246+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:30:28.157+0800","caller":"web_app/main.go:41","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:30:28.185+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"web_app/main.go:68","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:79","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:90","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:33:00.720+0800","caller":"web_app/main.go:40","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:33:00.731+0800","caller":"web_app/main.go:67","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:78","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:89","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:33:14.221+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T20:57:39.329+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T20:57:39.340+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:00:21.288+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:00:30.736+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:01:12.836+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:03.238+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:09.861+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:53.980+0800","caller":"logger/logger.go:80","msg":"/paramsignup","status":404,"method":"GET","path":"/paramsignup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:57.881+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-21T21:04:10.980+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:04:10.992+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:04:26.156+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '\"' after object key:value pair"}
{"level":"INFO","time":"2025-08-21T21:04:26.157+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:05:11.847+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:06:29.480+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:06:29.489+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:06:33.582+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:12:13.762+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:12:13.771+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:12:22.100+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006566}
{"level":"ERROR","time":"2025-08-21T21:12:30.834+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:12:30.834+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:22:39.786+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:22:48.751+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005049}
{"level":"ERROR","time":"2025-08-21T21:22:54.091+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:22:54.091+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006601}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:24:11.245+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
