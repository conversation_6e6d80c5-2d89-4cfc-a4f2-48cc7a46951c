{"level":"DEBUG","time":"2025-08-21T00:50:03.958+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:03.967+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:03.970+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"ERROR","time":"2025-08-21T00:50:03.975+0800","caller":"redis/redis.go:73","msg":"Redis 连接失败","error":"ERR Client sent AUTH, but no password is set"}
{"level":"INFO","time":"2025-08-21T00:50:03.976+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:50:39.263+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:50:39.270+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:50:39.272+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:50:39.273+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:51:16.625+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:56:47.078+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:56:47.086+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:56:47.087+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:56:47.088+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T00:57:03.970+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T00:57:03.971+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T00:57:11.973+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T00:57:11.982+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T00:57:11.984+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T00:57:11.985+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T00:57:58.820+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:57:59.083+0800","caller":"logger/logger.go:81","msg":"/favicon.ico","status":404,"method":"GET","path":"/favicon.ico","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:16.267+0800","caller":"logger/logger.go:81","msg":"/","status":404,"method":"GET","path":"/","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T00:58:28.264+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:00:56.113+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:00:56.114+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:04:33.668+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:04:33.676+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:04:33.678+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:04:33.679+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:04:40.377+0800","caller":"logger/logger.go:81","msg":"/ping","status":200,"method":"GET","path":"/ping","query":"","ip":"::1","user-agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T01:04:43.268+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:04:46.466+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:06:05.362+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:06:05.369+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:06:05.371+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:06:05.372+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:06:20.201+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:14:42.857+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:14:42.866+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:14:42.867+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"redis/redis.go:104","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:14:42.868+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"redis/redis.go:122","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:14:46.662+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:27:35.532+0800","caller":"web_app/main.go:53","msg":"日志系统初始化成功..."}
{"level":"DEBUG","time":"2025-08-21T01:27:35.542+0800","caller":"mysql/mysql.go:80","msg":"MySQL连接DSN","dsn":"root:***@tcp(127.0.0.1:3306)/web_app?charset=utf8mb4&parseTime=True&loc=Local"}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"mysql/mysql.go:108","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:27:35.544+0800","caller":"web_app/main.go:85","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:105","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:27:42.001+0800","caller":"web_app/main.go:118","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:27:42.002+0800","caller":"mysql/mysql.go:127","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T01:30:52.312+0800","caller":"web_app/main.go:37","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T01:30:52.321+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T01:30:52.322+0800","caller":"web_app/main.go:64","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:75","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"web_app/main.go:86","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T01:30:59.245+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T01:30:59.246+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:30:28.157+0800","caller":"web_app/main.go:41","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:30:28.185+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:30:28.187+0800","caller":"web_app/main.go:68","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:79","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"web_app/main.go:90","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:31:33.811+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T19:33:00.720+0800","caller":"web_app/main.go:40","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T19:33:00.730+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T19:33:00.731+0800","caller":"web_app/main.go:67","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:78","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"web_app/main.go:89","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T19:33:14.220+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T19:33:14.221+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T20:57:39.329+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T20:57:39.340+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T20:57:39.341+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:00:21.288+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:00:30.736+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:01:12.836+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:03.238+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:09.861+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:53.980+0800","caller":"logger/logger.go:80","msg":"/paramsignup","status":404,"method":"GET","path":"/paramsignup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:02:57.881+0800","caller":"logger/logger.go:80","msg":"/signup","status":404,"method":"GET","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"DEBUG","time":"2025-08-21T21:04:10.980+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:04:10.991+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:04:10.992+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:04:26.156+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '\"' after object key:value pair"}
{"level":"INFO","time":"2025-08-21T21:04:26.157+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:05:11.847+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:05:11.848+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:06:29.480+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:06:29.488+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:06:29.489+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:06:33.582+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:07:41.299+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:12:13.762+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:12:13.771+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:12:13.772+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:12:22.100+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006566}
{"level":"ERROR","time":"2025-08-21T21:12:30.834+0800","caller":"controllers/user.go:17","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:12:30.834+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:18:19.170+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:22:39.786+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:22:39.796+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:22:48.751+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005049}
{"level":"ERROR","time":"2025-08-21T21:22:54.091+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:22:54.091+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006601}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:24:11.244+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:24:11.245+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:25:01.483+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:25:01.492+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:25:01.493+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:25:01.493+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:25:56.757+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:27:41.093+0800","caller":"web_app/main.go:43","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:27:41.102+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:27:41.129+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:27:41.129+0800","caller":"web_app/main.go:76","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:27:50.952+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:27:50.953+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010038}
{"level":"INFO","time":"2025-08-21T21:28:23.110+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"web_app/main.go:87","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"web_app/main.go:98","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:38:40.126+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:38:40.127+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:38:56.526+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:38:56.536+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:38:56.537+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:38:56.537+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:39:11.834+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:39:11.834+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:39:22.855+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"json: cannot unmarshal number into Go struct field ParamSignUp.re_password of type string"}
{"level":"INFO","time":"2025-08-21T21:39:22.855+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:40:02.046+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:40:02.046+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:40:02.047+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:40:02.047+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:40:04.923+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:40:04.931+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:40:04.932+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:40:04.932+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-21T21:40:10.277+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:15.556+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:15.556+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:41.073+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:41.073+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:49.981+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.RePassword' Error:Field validation for 'RePassword' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:40:49.981+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:40:56.876+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"invalid character '}' looking for beginning of object key string"}
{"level":"INFO","time":"2025-08-21T21:40:56.876+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:41:00.758+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.RePassword' Error:Field validation for 'RePassword' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:41:00.758+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:43:56.845+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:43:59.999+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:44:00.008+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:44:00.009+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:44:00.009+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:44:04.952+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:44:04.952+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:46:44.443+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:44.443+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0010003}
{"level":"INFO","time":"2025-08-21T21:46:50.862+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T21:46:50.862+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T21:46:50.863+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T21:46:50.863+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T21:46:53.001+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T21:46:53.010+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T21:46:53.010+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T21:46:53.011+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T21:46:57.793+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:57.794+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0006399}
{"level":"ERROR","time":"2025-08-21T21:46:58.710+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-21T21:46:58.710+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:47:13.940+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:48:05.019+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T21:48:05.019+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T21:48:23.275+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"ERROR","time":"2025-08-21T21:49:53.037+0800","caller":"controllers/user.go:19","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T21:49:53.037+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T23:00:31.547+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T23:00:31.548+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-21T23:31:17.991+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-21T23:31:18.002+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-21T23:31:18.003+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-21T23:31:18.003+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-21T23:31:37.003+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'eqfield' tag"}
{"level":"INFO","time":"2025-08-21T23:31:37.003+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-21T23:31:51.244+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0153443}
{"level":"ERROR","time":"2025-08-21T23:32:06.818+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-21T23:32:06.818+0800","caller":"logger/logger.go:80","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005187}
{"level":"INFO","time":"2025-08-21T23:58:35.344+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-21T23:58:35.344+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-21T23:58:35.345+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-21T23:58:35.345+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T15:25:02.904+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T15:25:02.917+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T15:25:02.918+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T15:25:02.919+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T15:25:29.744+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-22T15:25:29.744+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0018587}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T15:26:30.067+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:18:43.903+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:18:43.915+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:18:43.916+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:18:43.916+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T17:18:54.330+0800","caller":"controllers/user.go:18","msg":"SignUp with invalid param","error":"Key: 'ParamSignUp.password' Error:Field validation for 'password' failed on the 'required' tag\nKey: 'ParamSignUp.re_password' Error:Field validation for 're_password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-22T17:18:54.331+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.001723}
{"level":"INFO","time":"2025-08-22T17:19:05.625+0800","caller":"logger/logger.go:94","msg":"/login","status":404,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:19:07.771+0800","caller":"logger/logger.go:94","msg":"/login","status":404,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:19:39.659+0800","caller":"logger/logger.go:94","msg":"/login/","status":404,"method":"POST","path":"/login/","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T17:20:45.523+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:20:48.848+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:20:48.858+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:20:48.858+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:20:48.859+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"ERROR","time":"2025-08-22T17:21:04.122+0800","caller":"controllers/user.go:56","msg":"Login with invalid param","error":"Key: 'ParamLogin.password' Error:Field validation for 'password' failed on the 'required' tag"}
{"level":"INFO","time":"2025-08-22T17:21:04.122+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.000681}
{"level":"ERROR","time":"2025-08-22T17:21:17.437+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:17.437+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0016917}
{"level":"ERROR","time":"2025-08-22T17:21:36.826+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:36.826+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005063}
{"level":"ERROR","time":"2025-08-22T17:21:51.948+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:21:51.949+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005093}
{"level":"ERROR","time":"2025-08-22T17:22:06.586+0800","caller":"controllers/user.go:39","msg":"logic.SignUp failed","error":"用户已存在"}
{"level":"INFO","time":"2025-08-22T17:22:06.586+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0005045}
{"level":"INFO","time":"2025-08-22T17:22:11.311+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0115282}
{"level":"ERROR","time":"2025-08-22T17:22:28.567+0800","caller":"controllers/user.go:77","msg":"logic.Login failed","error":"密码错误"}
{"level":"INFO","time":"2025-08-22T17:22:28.567+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.000537}
{"level":"INFO","time":"2025-08-22T17:23:20.750+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T17:23:20.751+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
{"level":"DEBUG","time":"2025-08-22T17:27:39.578+0800","caller":"web_app/main.go:44","msg":"日志系统初始化成功..."}
{"level":"INFO","time":"2025-08-22T17:27:39.595+0800","caller":"mysql/mysql.go:52","msg":"MySQL 数据库连接成功","host":"127.0.0.1","port":3306,"database":"web_app","max_open_conns":100,"max_idle_conns":20}
{"level":"INFO","time":"2025-08-22T17:27:39.596+0800","caller":"redis/redis.go:78","msg":"Redis 连接成功","host":"127.0.0.1","port":6379,"database":0,"pool_size":100}
{"level":"INFO","time":"2025-08-22T17:27:39.597+0800","caller":"web_app/main.go:82","msg":"正在启动HTTP服务器","port":8082}
{"level":"INFO","time":"2025-08-22T17:28:11.179+0800","caller":"logger/logger.go:94","msg":"/signup","status":200,"method":"POST","path":"/signup","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0083916}
{"level":"INFO","time":"2025-08-22T17:28:22.984+0800","caller":"logger/logger.go:94","msg":"/login","status":200,"method":"POST","path":"/login","query":"","ip":"127.0.0.1","user-agent":"Apifox/1.0.0 (https://apifox.com)","errors":"","cost":0.0008358}
{"level":"INFO","time":"2025-08-22T18:03:08.266+0800","caller":"web_app/main.go:93","msg":"收到关闭信号，开始优雅关机..."}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"web_app/main.go:104","msg":"服务器已安全退出"}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"redis/redis.go:96","msg":"Redis 连接已关闭"}
{"level":"INFO","time":"2025-08-22T18:03:08.267+0800","caller":"mysql/mysql.go:69","msg":"MySQL 连接已关闭"}
