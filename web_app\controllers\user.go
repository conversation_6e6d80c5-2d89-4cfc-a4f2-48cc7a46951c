package controllers

import (
	"fmt"
	"net/http"
	"web_app/logic"
	"web_app/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func SignUpHandler(c *gin.Context) {
	//1.获取参数并校验
	p := new(models.ParamSignUp)
	if err := c.ShouldBindJSON(&p); err != nil {
		//请求参数有误，直接返回响应
		zap.L().Error("SignUp with invalid param", zap.Error(err))
		c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
			"msg": "请求参数错误",
		})
		return
	}
	//手动对请求参数进行详细的业务规则校验
	// if len(p.Username) == 0 || len(p.Password) == 0 || len(p.RePassword) == 0 {
	// 	zap.L().Error("SignUp with invalid param")
	// 	c.<PERSON><PERSON>(http.StatusOK, gin.H{
	// 		"msg": "请求参数不能为空",
	// 	})
	// 	return
	// }
	fmt.Println(p)
	//2.业务处理
	logic.SignUp(p)
	//3.返回响应
	c.<PERSON>(http.StatusOK, gin.H{
		"msg": "ok",
	})
}
