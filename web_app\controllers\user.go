package controllers

import (
	"net/http"
	"web_app/logic"
	"web_app/models"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
)

func SignUpHandler(c *gin.Context) {
	//1.获取参数并校验
	p := new(models.ParamSignUp)
	if err := c.ShouldBindJSON(&p); err != nil {
		//请求参数有误，直接返回响应
		zap.L().Error("SignUp with invalid param", zap.Error(err))
		//判断err是否为validator.ValidationErrors类型
		if errs, ok := err.(validator.ValidationErrors); ok {
			//翻译错误
			errData := make(map[string]string)
			for _, e := range errs {
				errData[e.Field()] = e.Translate(trans)
			}
			c.JSON(http.StatusOK, gin.H{
				"msg": errData,
			})
			return
		}
		c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
			"msg": err.Error(),
		})
		return
	}
	//2.业务处理
	err := logic.SignUp(p)
	if err != nil {
		zap.L().Error("logic.SignUp failed", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{
			"msg": "注册失败",
		})
		return
	}
	//3.返回响应
	c.JSON(http.StatusOK, gin.H{
		"msg": "ok",
	})
}

func LoginHandler(c *gin.Context) {
	//1.获取参数并校验
	p := new(models.ParamLogin)
	if err := c.ShouldBindJSON(&p); err != nil {
		//请求参数有误，直接返回响应
		zap.L().Error("Login with invalid param", zap.Error(err))
		//判断err是否为validator.ValidationErrors类型
		if errs, ok := err.(validator.ValidationErrors); ok {
			//翻译错误
			errData := make(map[string]string)
			for _, e := range errs {
				errData[e.Field()] = e.Translate(trans)
			}
			c.JSON(http.StatusOK, gin.H{
				"msg": errData,
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"msg": err.Error(),
		})
		return
	}
	//2.业务处理
	err := logic.Login(p)
	if err != nil {
		zap.L().Error("logic.Login failed", zap.Error(err))
		c.JSON(http.StatusOK, gin.H{
			"msg": "登录失败",
		})
		return
	}
	//3.返回响应
	c.JSON(http.StatusOK, gin.H{
		"msg": "登录成功",
	})
}
