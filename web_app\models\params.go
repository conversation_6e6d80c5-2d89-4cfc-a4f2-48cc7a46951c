package models

//定义请求的参数结构体

type ParamSignUp struct {
	Username   string `json:"username" binding:"required" label:"用户名" msg:"用户名不能为空"`
	Password   string `json:"password" binding:"required" label:"密码" msg:"密码不能为空"`
	RePassword string `json:"re_password" binding:"required,eqfield=Password" label:"确认密码" msg:"两次密码不一致"`
}

type ParamLogin struct {
	Username string `json:"username" binding:"required" label:"用户名" msg:"用户名不能为空"`
	Password string `json:"password" binding:"required" label:"密码" msg:"密码不能为空"`
}
