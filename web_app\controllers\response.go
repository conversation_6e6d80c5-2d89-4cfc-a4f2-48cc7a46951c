package controllers

import (
	"net/http"

	"github.com/gin-gonic/gin"
)

type ResponseData struct {
	Code    ResCode     `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func ResponseError(c *gin.Context, code ResCode, message string) {
	c.JSON(http.StatusOK, &ResponseData{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

func ResponseErrorWithMsg(c *gin.Context, code ResCode, message string) {
	c.JSON(http.StatusOK, &ResponseData{
		Code:    code,
		Message: GetMsg(code),
		Data:    nil,
	})
}

func ResponseSuccess(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, &ResponseData{
		Code:    CodeSuccess,
		Message: GetMsg(CodeSuccess),
		Data:    data,
	})
}
