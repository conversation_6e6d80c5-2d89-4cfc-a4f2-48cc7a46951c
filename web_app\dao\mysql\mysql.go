// Package mysql 提供MySQL数据库连接管理
package mysql

import (
	"fmt"

	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

// db 全局数据库连接池实例
var db *sqlx.DB

// Init 初始化MySQL数据库连接
func Init() (err error) {
	// 读取数据库配置
	host := viper.GetString("mysql.host")
	port := viper.GetInt("mysql.port")
	user := viper.GetString("mysql.user")
	password := viper.GetString("mysql.password")
	database := viper.GetString("mysql.database")

	// 设置默认值
	if host == "" {
		host = "127.0.0.1"
	}
	if port == 0 {
		port = 3306
	}
	if user == "" {
		return fmt.Errorf("MySQL用户名不能为空")
	}
	if database == "" {
		return fmt.Errorf("MySQL数据库名不能为空")
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		user, password, host, port, database)

	// 连接数据库
	db, err = sqlx.Connect("mysql", dsn)
	if err != nil {
		zap.L().Error("MySQL 数据库连接失败", zap.Error(err))
		return err
	}

	// 配置连接池
	db.SetMaxOpenConns(viper.GetInt("mysql.max_open_conns"))
	db.SetMaxIdleConns(viper.GetInt("mysql.max_idle_conns"))
	zap.L().Info("MySQL 数据库连接成功",
		zap.String("host", viper.GetString("mysql.host")),
		zap.Int("port", viper.GetInt("mysql.port")),
		zap.String("database", viper.GetString("mysql.database")),
		zap.Int("max_open_conns", viper.GetInt("mysql.max_open_conns")),
		zap.Int("max_idle_conns", viper.GetInt("mysql.max_idle_conns")),
	)

	return nil
}

// Close 关闭数据库连接
func Close() {
	if db != nil {
		if err := db.Close(); err != nil {
			zap.L().Error("关闭 MySQL 连接失败", zap.Error(err))
		} else {
			zap.L().Info("MySQL 连接已关闭")
		}
	}
}

// GetDB 获取数据库连接实例
func GetDB() *sqlx.DB {
	return db
}
