package logic

import (
	"errors"
	"web_app/dao/mysql"
	"web_app/models"

	"web_app/pkg/snowflake"
)

func SignUp(p *models.ParamSignUp) (err error) {
	// 1.判断用户是否存在
	if exist, err := mysql.CheckUserExist(p.Username); err != nil {
		//数据库查询出错
		return err
	} else if exist {
		//用户已存在
		return errors.New("用户已存在")
	}
	// 2.生成UID
	userID := snowflake.GenID()
	user := &models.User{
		UserID:   userID,
		Username: p.Username,
		Password: p.Password,
	}
	// 3.保存进数据库
	mysql.InsertUser(user)
	return
}

func Login(p *models.ParamLogin) (err error) {
	user := &models.User{
		Username: p.Username,
		Password: p.Password,
	}
	return mysql.Login(user)
}
